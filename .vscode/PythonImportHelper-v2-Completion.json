[{"label": "admin", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "admin", "importPath": "django.contrib", "description": "django.contrib", "isExtraImport": true, "detail": "django.contrib", "documentation": {}}, {"label": "AppConfig", "importPath": "django.apps", "description": "django.apps", "isExtraImport": true, "detail": "django.apps", "documentation": {}}, {"label": "models", "importPath": "django.db", "description": "django.db", "isExtraImport": true, "detail": "django.db", "documentation": {}}, {"label": "TestCase", "importPath": "django.test", "description": "django.test", "isExtraImport": true, "detail": "django.test", "documentation": {}}, {"label": "render", "importPath": "django.shortcuts", "description": "django.shortcuts", "isExtraImport": true, "detail": "django.shortcuts", "documentation": {}}, {"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "JsonResponse", "importPath": "django.http", "description": "django.http", "isExtraImport": true, "detail": "django.http", "documentation": {}}, {"label": "root_agent", "importPath": "agents", "description": "agents", "isExtraImport": true, "detail": "agents", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "get_asgi_application", "importPath": "django.core.asgi", "description": "django.core.asgi", "isExtraImport": true, "detail": "django.core.asgi", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "path", "importPath": "django.urls", "description": "django.urls", "isExtraImport": true, "detail": "django.urls", "documentation": {}}, {"label": "ask_view", "importPath": "adk_agent_app.views", "description": "adk_agent_app.views", "isExtraImport": true, "detail": "adk_agent_app.views", "documentation": {}}, {"label": "get_wsgi_application", "importPath": "django.core.wsgi", "description": "django.core.wsgi", "isExtraImport": true, "detail": "django.core.wsgi", "documentation": {}}, {"label": "google_Search", "importPath": "google.adk.tools", "description": "google.adk.tools", "isExtraImport": true, "detail": "google.adk.tools", "documentation": {}}, {"label": "LlmAgent", "importPath": "google.adk.agents", "description": "google.adk.agents", "isExtraImport": true, "detail": "google.adk.agents", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "AdkAgentAppConfig", "kind": 6, "importPath": "adk_agent_app.apps", "description": "adk_agent_app.apps", "peekOfCode": "class AdkAgentAppConfig(AppConfig):\n    default_auto_field = \"django.db.models.BigAutoField\"\n    name = \"adk_agent_app\"", "detail": "adk_agent_app.apps", "documentation": {}}, {"label": "ask_view", "kind": 2, "importPath": "adk_agent_app.views", "description": "adk_agent_app.views", "peekOfCode": "def ask_view(request):\n    query = request.GET.get(\"q\")\n    if not query:\n        return JsonResponse({\"error\": \"Missing query\"}, status=400)\n    output = asyncio.run(run_agent(query))\n    return JsonResponse({\"response\": output})", "detail": "adk_agent_app.views", "documentation": {}}, {"label": "application", "kind": 5, "importPath": "adk_django_test.asgi", "description": "adk_django_test.asgi", "peekOfCode": "application = get_asgi_application()", "detail": "adk_django_test.asgi", "documentation": {}}, {"label": "BASE_DIR", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "BASE_DIR = Path(__file__).resolve().parent.parent\n# Quick-start development settings - unsuitable for production\n# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/\n# SECURITY WARNING: keep the secret key used in production secret!\nSECRET_KEY = \"django-insecure-+e_7qrm+hvmemditv@%iirurtr)7#!u&*lnto*c%cxg+p&_ch9\"\n# SECURITY WARNING: don't run with debug turned on in production!\nDEBUG = True\nALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "SECRET_KEY", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "SECRET_KEY = \"django-insecure-+e_7qrm+hvmemditv@%iirurtr)7#!u&*lnto*c%cxg+p&_ch9\"\n# SECURITY WARNING: don't run with debug turned on in production!\nDEBUG = True\nALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [\n    \"django.contrib.admin\",\n    \"django.contrib.auth\",\n    \"django.contrib.contenttypes\",\n    \"django.contrib.sessions\",", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "DEBUG", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "DEBUG = True\nALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [\n    \"django.contrib.admin\",\n    \"django.contrib.auth\",\n    \"django.contrib.contenttypes\",\n    \"django.contrib.sessions\",\n    \"django.contrib.messages\",\n    \"django.contrib.staticfiles\",", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "ALLOWED_HOSTS", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "ALLOWED_HOSTS = []\n# Application definition\nINSTALLED_APPS = [\n    \"django.contrib.admin\",\n    \"django.contrib.auth\",\n    \"django.contrib.contenttypes\",\n    \"django.contrib.sessions\",\n    \"django.contrib.messages\",\n    \"django.contrib.staticfiles\",\n    'adk_agent_app',", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "INSTALLED_APPS", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "INSTALLED_APPS = [\n    \"django.contrib.admin\",\n    \"django.contrib.auth\",\n    \"django.contrib.contenttypes\",\n    \"django.contrib.sessions\",\n    \"django.contrib.messages\",\n    \"django.contrib.staticfiles\",\n    'adk_agent_app',\n]\nMIDDLEWARE = [", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "MIDDLEWARE", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "MIDDLEWARE = [\n    \"django.middleware.security.SecurityMiddleware\",\n    \"django.contrib.sessions.middleware.SessionMiddleware\",\n    \"django.middleware.common.CommonMiddleware\",\n    \"django.middleware.csrf.CsrfViewMiddleware\",\n    \"django.contrib.auth.middleware.AuthenticationMiddleware\",\n    \"django.contrib.messages.middleware.MessageMiddleware\",\n    \"django.middleware.clickjacking.XFrameOptionsMiddleware\",\n]\nROOT_URLCONF = \"adk_django_test.urls\"", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "ROOT_URLCONF", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "ROOT_URLCONF = \"adk_django_test.urls\"\nTEMPLATES = [\n    {\n        \"BACKEND\": \"django.template.backends.django.DjangoTemplates\",\n        \"DIRS\": [],\n        \"APP_DIRS\": True,\n        \"OPTIONS\": {\n            \"context_processors\": [\n                \"django.template.context_processors.request\",\n                \"django.contrib.auth.context_processors.auth\",", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "TEMPLATES", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "TEMPLATES = [\n    {\n        \"BACKEND\": \"django.template.backends.django.DjangoTemplates\",\n        \"DIRS\": [],\n        \"APP_DIRS\": True,\n        \"OPTIONS\": {\n            \"context_processors\": [\n                \"django.template.context_processors.request\",\n                \"django.contrib.auth.context_processors.auth\",\n                \"django.contrib.messages.context_processors.messages\",", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "WSGI_APPLICATION", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "WSGI_APPLICATION = \"adk_django_test.wsgi.application\"\n# Database\n# https://docs.djangoproject.com/en/5.2/ref/settings/#databases\nDATABASES = {\n    \"default\": {\n        \"ENGINE\": \"django.db.backends.sqlite3\",\n        \"NAME\": BASE_DIR / \"db.sqlite3\",\n    }\n}\n# Password validation", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "DATABASES", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "DATABASES = {\n    \"default\": {\n        \"ENGINE\": \"django.db.backends.sqlite3\",\n        \"NAME\": BASE_DIR / \"db.sqlite3\",\n    }\n}\n# Password validation\n# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators\nAUTH_PASSWORD_VALIDATORS = [\n    {", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "AUTH_PASSWORD_VALIDATORS", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "AUTH_PASSWORD_VALIDATORS = [\n    {\n        \"NAME\": \"django.contrib.auth.password_validation.UserAttributeSimilarityValidator\",\n    },\n    {\n        \"NAME\": \"django.contrib.auth.password_validation.MinimumLengthValidator\",\n    },\n    {\n        \"NAME\": \"django.contrib.auth.password_validation.CommonPasswordValidator\",\n    },", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "LANGUAGE_CODE", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "LANGUAGE_CODE = \"en-us\"\nTIME_ZONE = \"UTC\"\nUSE_I18N = True\nUSE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/5.2/howto/static-files/\nSTATIC_URL = \"static/\"\n# Default primary key field type\n# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "TIME_ZONE", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "TIME_ZONE = \"UTC\"\nUSE_I18N = True\nUSE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/5.2/howto/static-files/\nSTATIC_URL = \"static/\"\n# Default primary key field type\n# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "USE_I18N", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "USE_I18N = True\nUSE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/5.2/howto/static-files/\nSTATIC_URL = \"static/\"\n# Default primary key field type\n# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "USE_TZ", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "USE_TZ = True\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/5.2/howto/static-files/\nSTATIC_URL = \"static/\"\n# Default primary key field type\n# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "STATIC_URL", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "STATIC_URL = \"static/\"\n# Default primary key field type\n# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field\nDEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "DEFAULT_AUTO_FIELD", "kind": 5, "importPath": "adk_django_test.settings", "description": "adk_django_test.settings", "peekOfCode": "DEFAULT_AUTO_FIELD = \"django.db.models.BigAutoField\"", "detail": "adk_django_test.settings", "documentation": {}}, {"label": "url<PERSON><PERSON><PERSON>", "kind": 5, "importPath": "adk_django_test.urls", "description": "adk_django_test.urls", "peekOfCode": "urlpatterns = [\n    path(\"admin/\", admin.site.urls),\n    path('ask/', ask_view),\n]", "detail": "adk_django_test.urls", "documentation": {}}, {"label": "application", "kind": 5, "importPath": "adk_django_test.wsgi", "description": "adk_django_test.wsgi", "peekOfCode": "application = get_wsgi_application()", "detail": "adk_django_test.wsgi", "documentation": {}}, {"label": "root_agent", "kind": 5, "importPath": "agents.agent", "description": "agents.agent", "peekOfCode": "root_agent = LlmAgent(\n    name=\"root_agent\",\n    model=\"gemini-2.0-pro\",\n    instruction=\"Answer using search results.\",\n    description=\"Google-powered search agent.\",\n    tools=[google_Search()],\n)", "detail": "agents.agent", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "manage", "description": "manage", "peekOfCode": "def main():\n    \"\"\"Run administrative tasks.\"\"\"\n    os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"adk_django_test.settings\")\n    try:\n        from django.core.management import execute_from_command_line\n    except ImportError as exc:\n        raise ImportError(\n            \"Couldn't import Django. Are you sure it's installed and \"\n            \"available on your PYTHONPATH environment variable? Did you \"\n            \"forget to activate a virtual environment?\"", "detail": "manage", "documentation": {}}]