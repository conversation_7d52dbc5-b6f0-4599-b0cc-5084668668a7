from google.adk.agents import LlmAgent
from adk_agent_app.utils import calculate
import os
from dotenv import load_dotenv

# Load the .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '../.env'))


def calculator_tool(operation: str, a: float, b: float) -> str:
    """
    A calculator tool that performs basic mathematical operations.

    Args:
        operation: The mathematical operation to perform (add, subtract, multiply, divide)
        a: The first number
        b: The second number

    Returns:
        A string containing the result of the calculation
    """
    try:
        result = calculate(operation, a, b)
        return f"The result of {a} {operation} {b} is {result}"
    except ValueError as e:
        return f"Error: {str(e)}"
    except Exception as e:
        return f"Unexpected error: {str(e)}"


calculator_agent = LlmAgent(
    name="calculator_agent",
    model="gemini-2.0-flash",
    instruction="""You are a helpful calculator assistant. You can perform basic mathematical operations using the calculator tool.

When a user asks you to perform calculations:
1. Identify the numbers and operation from their request
2. Use the calculator_tool to perform the calculation
3. Provide a clear response with the result

You can perform these operations:
- Addition (add, +)
- Subtraction (subtract, -)
- Multiplication (multiply, *)
- Division (divide, /)

Examples:
- "What is 5 + 3?" -> Use calculator_tool("add", 5, 3)
- "Calculate 10 minus 4" -> Use calculator_tool("subtract", 10, 4)
- "Multiply 7 by 8" -> Use calculator_tool("multiply", 7, 8)
- "Divide 20 by 4" -> Use calculator_tool("divide", 20, 4)

Always be helpful and provide clear explanations of the calculations.""",
    description="A calculator agent that can perform basic mathematical operations.",
    tools=[calculator_tool],
)
