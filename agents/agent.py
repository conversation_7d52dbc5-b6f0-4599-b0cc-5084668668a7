from google.adk.tools import google_search
from google.adk.agents import LlmAgent
import os
from dotenv import load_dotenv

# Load the .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '../.env'))


root_agent = LlmAgent(
    name="root_agent",
    model="gemini-2.0-flash",
    instruction="Answer using search results.",
    description="Google-powered search agent.",
    tools=[google_search],
)
