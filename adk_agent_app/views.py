from agents.calculator_agent import calculator_agent
from django.shortcuts import render

# Create your views here.
import asyncio
from django.http import JsonResponse
from agents.agent import root_agent
from agents.coordinator_agent import coordinator_agent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types
from .utils import calculate

# Initialize session service and runners
session_service = InMemorySessionService()
runner = Runner(
    agent=root_agent,
    app_name="django_adk_app",
    session_service=session_service
)

# Import calculator_agent after other imports to avoid circular imports

calculator_runner = Runner(
    agent=calculator_agent,
    app_name="django_calculator_app",
    session_service=session_service
)

coordinator_runner = Runner(
    agent=coordinator_agent,
    app_name="django_coordinator_app",
    session_service=session_service
)

smart_runner = Runner(
    agent=coordinator_agent,
    app_name="django_smart_app",
    session_service=session_service
)


async def run_agent(query: str):
    user_id = "django_user"
    session_id = "django_session"

    # Create session if it doesn't exist
    try:
        await session_service.create_session(
            app_name="django_adk_app",
            user_id=user_id,
            session_id=session_id
        )
    except:
        # Session might already exist
        pass

    # Create user content
    user_content = types.Content(role='user', parts=[types.Part(text=query)])

    final_response = ""
    async for event in runner.run_async(
        user_id=user_id,
        session_id=session_id,
        new_message=user_content
    ):
        if event.is_final_response() and event.content and event.content.parts:
            for part in event.content.parts:
                if part.text:
                    final_response += part.text

    return final_response


# Import calculator_agent after it's defined to avoid circular imports


async def run_calculator_agent(query: str):
    user_id = "calculator_user"
    session_id = "calculator_session"

    # Create session if it doesn't exist
    try:
        await session_service.create_session(
            app_name="django_calculator_app",
            user_id=user_id,
            session_id=session_id
        )
    except:
        # Session might already exist
        pass

    # Create user content
    user_content = types.Content(role='user', parts=[types.Part(text=query)])

    final_response = ""
    async for event in calculator_runner.run_async(
        user_id=user_id,
        session_id=session_id,
        new_message=user_content
    ):
        if event.is_final_response() and event.content and event.content.parts:
            for part in event.content.parts:
                if part.text:
                    final_response += part.text

    return final_response


def ask_view(request):
    query = request.GET.get("q")
    if not query:
        return JsonResponse({"error": "Missing query"}, status=400)
    output = asyncio.run(run_agent(query))
    return JsonResponse({"response": output})


async def run_smart_agent(query: str):
    user_id = "smart_user"
    session_id = "smart_session"

    # Create session if it doesn't exist
    try:
        await session_service.create_session(
            app_name="django_smart_app",
            user_id=user_id,
            session_id=session_id
        )
    except:
        # Session might already exist
        pass

    # Create user content
    user_content = types.Content(role='user', parts=[types.Part(text=query)])

    final_response = ""
    async for event in smart_runner.run_async(
        user_id=user_id,
        session_id=session_id,
        new_message=user_content
    ):
        if event.is_final_response() and event.content and event.content.parts:
            for part in event.content.parts:
                if part.text:
                    final_response += part.text

    return final_response


def calculator_view(request):
    query = request.GET.get("q")
    if not query:
        return JsonResponse({"error": "Missing query parameter 'q'"}, status=400)

    try:
        output = asyncio.run(run_calculator_agent(query))
        return JsonResponse({"response": output})
    except Exception as e:
        return JsonResponse({"error": f"An error occurred: {str(e)}"}, status=500)


def smart_view(request):
    query = request.GET.get("q")
    if not query:
        return JsonResponse({"error": "Missing query parameter 'q'"}, status=400)

    try:
        output = asyncio.run(run_smart_agent(query))
        return JsonResponse({"response": output})
    except Exception as e:
        return JsonResponse({"error": f"An error occurred: {str(e)}"}, status=500)
