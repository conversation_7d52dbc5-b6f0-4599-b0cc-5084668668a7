from django.shortcuts import render

# Create your views here.
import asyncio
from django.http import JsonResponse
from agents.agent import root_agent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types

# Initialize session service and runner
session_service = InMemorySessionService()
runner = Runner(
    agent=root_agent,
    app_name="django_adk_app",
    session_service=session_service
)


async def run_agent(query: str):
    user_id = "django_user"
    session_id = "django_session"

    # Create session if it doesn't exist
    try:
        await session_service.create_session(
            app_name="django_adk_app",
            user_id=user_id,
            session_id=session_id
        )
    except:
        # Session might already exist
        pass

    # Create user content
    user_content = types.Content(role='user', parts=[types.Part(text=query)])

    final_response = ""
    async for event in runner.run_async(
        user_id=user_id,
        session_id=session_id,
        new_message=user_content
    ):
        if event.is_final_response() and event.content and event.content.parts:
            for part in event.content.parts:
                if part.text:
                    final_response += part.text

    return final_response


def ask_view(request):
    query = request.GET.get("q")
    if not query:
        return JsonResponse({"error": "Missing query"}, status=400)
    output = asyncio.run(run_agent(query))
    return JsonResponse({"response": output})
