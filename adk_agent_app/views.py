from django.shortcuts import render

# Create your views here.
import asyncio
from django.http import JsonResponse
from agents import root_agent


async def run_agent(query: str):
    result = await root_agent.run(query)
    return result.output


def ask_view(request):
    query = request.GET.get("q")
    if not query:
        return JsonResponse({"error": "Missing query"}, status=400)
    output = asyncio.run(run_agent(query))
    return JsonResponse({"response": output})
