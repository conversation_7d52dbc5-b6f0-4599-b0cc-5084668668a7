"""
Utility functions for the ADK agent app.
"""


def calculate(operation: str, a: float, b: float) -> float:
    """
    Performs basic mathematical operations.
    
    Args:
        operation: The operation to perform ('add', 'subtract', 'multiply', 'divide')
        a: First number
        b: Second number
    
    Returns:
        The result of the calculation
    
    Raises:
        ValueError: If operation is not supported or division by zero
    """
    operation = operation.lower().strip()
    
    if operation in ['add', '+', 'addition']:
        return a + b
    elif operation in ['subtract', '-', 'subtraction']:
        return a - b
    elif operation in ['multiply', '*', 'multiplication']:
        return a * b
    elif operation in ['divide', '/', 'division']:
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b
    else:
        raise ValueError(
            f"Unsupported operation: {operation}. Supported operations: add, subtract, multiply, divide")
